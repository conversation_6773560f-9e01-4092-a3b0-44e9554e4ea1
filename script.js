// DOM Elements
const navbar = document.querySelector('.navbar');
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const productCards = document.querySelectorAll('.product-card');
const filterBtns = document.querySelectorAll('.filter-btn');
const cartCount = document.querySelector('.cart-count');

// Global Variables
let cart = [];
let currentFilter = 'all';

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    initializeAnimations();
    initializeProductFilters();
    initializeCart();
    initializeForms();
    initializeScrollEffects();
});

// Navbar scroll effect
window.addEventListener('scroll', function() {
    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(44, 62, 80, 0.95)';
        navbar.style.backdropFilter = 'blur(10px)';
    } else {
        navbar.style.background = 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)';
        navbar.style.backdropFilter = 'none';
    }
});

// Mobile menu toggle
hamburger?.addEventListener('click', function() {
    navMenu.classList.toggle('active');
    hamburger.classList.toggle('active');
});

// Smooth scrolling
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Product filtering
function initializeProductFilters() {
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter products
            filterProducts(filter);
        });
    });
}

function filterProducts(filter) {
    currentFilter = filter;
    
    productCards.forEach(card => {
        const category = card.getAttribute('data-category');
        
        if (filter === 'all' || category === filter) {
            card.style.display = 'block';
            card.classList.add('fade-in');
        } else {
            card.style.display = 'none';
            card.classList.remove('fade-in');
        }
    });
}

// Cart functionality
function initializeCart() {
    const addToCartBtns = document.querySelectorAll('.btn-add-cart');
    
    addToCartBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const productCard = this.closest('.product-card');
            const productName = productCard.querySelector('h3').textContent;
            const productPrice = productCard.querySelector('.product-price').textContent;
            
            addToCart({
                name: productName,
                price: productPrice,
                id: Date.now()
            });
            
            // Visual feedback
            this.textContent = 'เพิ่มแล้ว!';
            this.style.background = '#27ae60';
            
            setTimeout(() => {
                this.textContent = 'เพิ่มในตะกร้า';
                this.style.background = '#3498db';
            }, 1500);
        });
    });
}

function addToCart(product) {
    cart.push(product);
    updateCartCount();
    showNotification('เพิ่มสินค้าในตะกร้าแล้ว!');
}

function updateCartCount() {
    if (cartCount) {
        cartCount.textContent = cart.length;
        cartCount.style.animation = 'bounce 0.5s ease';
        
        setTimeout(() => {
            cartCount.style.animation = '';
        }, 500);
    }
}

// Modal functionality
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        // Add click outside to close
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal(modalId);
            }
        });
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
        document.body.style.overflow = 'auto';
    }
}

function switchModal(currentModalId, targetModalId) {
    closeModal(currentModalId);
    setTimeout(() => {
        openModal(targetModalId);
    }, 300);
}

// Form handling
function initializeForms() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const contactForm = document.querySelector('.contact-form form');
    
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    if (contactForm) {
        contactForm.addEventListener('submit', handleContact);
    }
}

function handleLogin(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const email = formData.get('email');
    const password = formData.get('password');
    
    // Simulate login process
    showLoading(e.target.querySelector('button[type="submit"]'));
    
    setTimeout(() => {
        hideLoading(e.target.querySelector('button[type="submit"]'));
        showNotification('เข้าสู่ระบบสำเร็จ!', 'success');
        closeModal('loginModal');
        
        // Update UI for logged in user
        updateUserInterface(email);
    }, 2000);
}

function handleRegister(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');
    
    if (password !== confirmPassword) {
        showNotification('รหัสผ่านไม่ตรงกัน!', 'error');
        return;
    }
    
    // Simulate registration process
    showLoading(e.target.querySelector('button[type="submit"]'));
    
    setTimeout(() => {
        hideLoading(e.target.querySelector('button[type="submit"]'));
        showNotification('สมัครสมาชิกสำเร็จ!', 'success');
        closeModal('registerModal');
        
        // Auto login after registration
        const email = formData.get('email');
        updateUserInterface(email);
    }, 2000);
}

function handleContact(e) {
    e.preventDefault();
    const submitBtn = e.target.querySelector('.btn-submit');
    
    showLoading(submitBtn);
    
    setTimeout(() => {
        hideLoading(submitBtn);
        showNotification('ส่งข้อความสำเร็จ! เราจะติดต่อกลับเร็วๆ นี้', 'success');
        e.target.reset();
    }, 2000);
}

// UI Updates
function updateUserInterface(email) {
    const loginBtn = document.querySelector('.btn-login');
    const registerBtn = document.querySelector('.btn-register');
    
    if (loginBtn && registerBtn) {
        loginBtn.textContent = 'สวัสดี!';
        loginBtn.style.pointerEvents = 'none';
        registerBtn.style.display = 'none';
    }
}

// Loading states
function showLoading(button) {
    const originalText = button.textContent;
    button.setAttribute('data-original-text', originalText);
    button.textContent = 'กำลังดำเนินการ...';
    button.disabled = true;
    button.style.opacity = '0.7';
}

function hideLoading(button) {
    const originalText = button.getAttribute('data-original-text');
    button.textContent = originalText;
    button.disabled = false;
    button.style.opacity = '1';
}

// Notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Styles
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '10px',
        color: 'white',
        fontWeight: '500',
        zIndex: '3000',
        animation: 'slideIn 0.3s ease',
        maxWidth: '300px'
    });
    
    // Colors based on type
    const colors = {
        success: '#27ae60',
        error: '#e74c3c',
        info: '#3498db'
    };
    
    notification.style.background = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    // Auto remove
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Scroll animations
function initializeScrollEffects() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('slide-up');
            }
        });
    }, observerOptions);
    
    // Observe elements
    const elementsToAnimate = document.querySelectorAll('.product-card, .feature-item, .contact-item');
    elementsToAnimate.forEach(el => observer.observe(el));
}

// Initialize animations
function initializeAnimations() {
    // Add CSS for slide animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideOut {
            to { transform: translateX(100%); opacity: 0; }
        }
        
        .notification {
            transform: translateX(100%);
            animation: slideIn 0.3s ease forwards;
        }
    `;
    document.head.appendChild(style);
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            closeModal(openModal.id);
        }
    }
});

// Prevent form submission on demo
window.addEventListener('beforeunload', function(e) {
    if (cart.length > 0) {
        e.preventDefault();
        e.returnValue = '';
    }
});
