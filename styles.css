/* Reset และ Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Kanit', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo h1 {
    color: #ecf0f1;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: -5px;
}

.logo span {
    color: #bdc3c7;
    font-size: 0.9rem;
    font-weight: 300;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #ecf0f1;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: #3498db;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #3498db;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-buttons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-login, .btn-register {
    padding: 0.5rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
}

.btn-login {
    background: transparent;
    color: #ecf0f1;
    border: 2px solid #ecf0f1;
}

.btn-login:hover {
    background: #ecf0f1;
    color: #2c3e50;
}

.btn-register {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: 2px solid transparent;
}

.btn-register:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.cart-icon {
    position: relative;
    color: #ecf0f1;
    font-size: 1.2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.cart-icon:hover {
    color: #3498db;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #ecf0f1;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #4a5f7a 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    margin-top: 80px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-text {
    color: #ecf0f1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    background: linear-gradient(135deg, #ecf0f1, #3498db);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #bdc3c7;
    line-height: 1.6;
}

.btn-hero {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
    box-shadow: 0 5px 20px rgba(52, 152, 219, 0.3);
}

.btn-hero:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(52, 152, 219, 0.5);
}

.hero-image {
    position: relative;
    height: 500px;
}

.floating-shoes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shoe-item {
    position: absolute;
    width: 150px;
    height: 100px;
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: float 6s ease-in-out infinite;
}

.shoe-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shoe-2 {
    top: 50%;
    right: 20%;
    animation-delay: 2s;
}

.shoe-3 {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid #ecf0f1;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0) rotate(-45deg); }
    40% { transform: translateY(-10px) rotate(-45deg); }
    60% { transform: translateY(-5px) rotate(-45deg); }
}

/* Products Section */
.products {
    padding: 5rem 0;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #2c3e50;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 2px;
}

.product-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.8rem 2rem;
    border: 2px solid #bdc3c7;
    background: transparent;
    color: #7f8c8d;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-color: #3498db;
    transform: translateY(-2px);
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.product-image {
    height: 250px;
    background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
    position: relative;
    overflow: hidden;
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #3498db;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.sale {
    background: #e74c3c;
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(44, 62, 80, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.btn-quick-view,
.btn-add-cart {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
}

.btn-quick-view {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-quick-view:hover {
    background: white;
    color: #2c3e50;
}

.btn-add-cart {
    background: #3498db;
    color: white;
}

.btn-add-cart:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.product-info {
    padding: 1.5rem;
}

.product-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.old-price {
    text-decoration: line-through;
    color: #95a5a6;
    font-size: 1rem;
    margin-right: 0.5rem;
}

.new-price {
    color: #e74c3c;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.product-rating i {
    color: #f39c12;
    font-size: 0.9rem;
}

.product-rating span {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-left: 0.5rem;
}

/* About Section */
.about {
    padding: 5rem 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.about-text p {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.features {
    display: grid;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.feature-item i {
    font-size: 2rem;
    color: #3498db;
    margin-top: 0.5rem;
}

.feature-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.feature-item p {
    color: #7f8c8d;
    line-height: 1.6;
}

.about-image {
    height: 400px;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.image-placeholder::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: rgba(52, 152, 219, 0.3);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-item i {
    font-size: 1.5rem;
    color: #3498db;
    margin-top: 0.5rem;
}

.contact-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.contact-item p {
    color: #7f8c8d;
    line-height: 1.6;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    font-family: 'Kanit', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.btn-submit {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    cursor: pointer;
    font-family: 'Kanit', sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ecf0f1;
}

.footer-section p {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #3498db;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(52, 152, 219, 0.2);
    border-radius: 50%;
    color: #ecf0f1;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #3498db;
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #ecf0f1;
}

.modal-header h2 {
    color: #2c3e50;
    font-weight: 600;
}

.close {
    font-size: 2rem;
    color: #95a5a6;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #e74c3c;
}

.modal-body {
    padding: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.modal .form-group {
    margin-bottom: 1.5rem;
}

.modal .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 500;
}

.modal .form-group input {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-family: 'Kanit', sans-serif;
    transition: all 0.3s ease;
}

.modal .form-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #7f8c8d;
}

.checkbox-container input {
    margin-right: 0.5rem;
}

.forgot-password {
    color: #3498db;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #2980b9;
}

.btn-modal {
    width: 100%;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Kanit', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-modal:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.modal-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #ecf0f1;
}

.modal-footer p {
    color: #7f8c8d;
}

.modal-footer a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.modal-footer a:hover {
    color: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .nav-buttons {
        gap: 0.5rem;
    }

    .btn-login,
    .btn-register {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .product-filters {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-body {
        padding: 1.5rem;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-up {
    animation: slideUp 0.5s ease;
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
